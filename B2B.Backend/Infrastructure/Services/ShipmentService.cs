using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Enums;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Services;

public class ShipmentService : IShipmentService
{
    private readonly IGenericRepository<Shipment> _shipmentRepository;
    private readonly IGenericRepository<Order> _orderRepository;
    private readonly ICurrentUserService _currentUserService;

    public ShipmentService(
        IGenericRepository<Shipment> shipmentRepository,
        IGenericRepository<Order> orderRepository,
        ICurrentUserService currentUserService)
    {
        _shipmentRepository = shipmentRepository;
        _orderRepository = orderRepository;
        _currentUserService = currentUserService;
    }

    public async Task<IEnumerable<ShipmentDto>> GetAllAsync()
    {
        var shipments = await _shipmentRepository.Query()
            .Include(s => s.Order)
                .ThenInclude(o => o.Customer)
            .Where(s => !s.IsDeleted)
            .OrderByDescending(s => s.CreatedAt)
            .ToListAsync();

        return shipments.Select(s => new ShipmentDto
        {
            Id = s.Id,
            OrderId = s.OrderId,
            TrackingNumber = s.TrackingNumber,
            Carrier = s.Carrier,
            Status = s.Status,
            ShippedAt = s.ShippedAt,
            DeliveredAt = s.DeliveredAt,
            Notes = s.Notes,
            CreatedAt = s.CreatedAt
        });
    }

    public async Task<ShipmentDto?> GetByIdAsync(Guid id)
    {
        var shipment = await _shipmentRepository.Query()
            .Include(s => s.Order)
                .ThenInclude(o => o.Customer)
            .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);

        if (shipment == null) return null;

        return new ShipmentDto
        {
            Id = shipment.Id,
            OrderId = shipment.OrderId,
            TrackingNumber = shipment.TrackingNumber,
            Carrier = shipment.Carrier,
            Status = shipment.Status,
            ShippedAt = shipment.ShippedAt,
            DeliveredAt = shipment.DeliveredAt,
            Notes = shipment.Notes,
            CreatedAt = shipment.CreatedAt
        };
    }

    public async Task<IEnumerable<ShipmentDto>> GetByOrderIdAsync(Guid orderId)
    {
        var shipments = await _shipmentRepository.Query()
            .Include(s => s.Order)
                .ThenInclude(o => o.Customer)
            .Where(s => s.OrderId == orderId && !s.IsDeleted)
            .OrderByDescending(s => s.CreatedAt)
            .ToListAsync();

        return shipments.Select(s => new ShipmentDto
        {
            Id = s.Id,
            OrderId = s.OrderId,
            TrackingNumber = s.TrackingNumber,
            Carrier = s.Carrier,
            Status = s.Status,
            ShippedAt = s.ShippedAt,
            DeliveredAt = s.DeliveredAt,
            Notes = s.Notes,
            CreatedAt = s.CreatedAt
        });
    }

    public async Task<IEnumerable<ShipmentDto>> GetByStatusAsync(ShipmentStatus status)
    {
        var shipments = await _shipmentRepository.Query()
            .Include(s => s.Order)
                .ThenInclude(o => o.Customer)
            .Where(s => s.Status == status && !s.IsDeleted)
            .OrderByDescending(s => s.CreatedAt)
            .ToListAsync();

        return shipments.Select(s => new ShipmentDto
        {
            Id = s.Id,
            OrderId = s.OrderId,
            TrackingNumber = s.TrackingNumber,
            Carrier = s.Carrier,
            Status = s.Status,
            ShippedAt = s.ShippedAt,
            DeliveredAt = s.DeliveredAt,
            Notes = s.Notes,
            CreatedAt = s.CreatedAt
        });
    }

    public async Task<IEnumerable<ShipmentDto>> SearchAsync(string searchTerm)
    {
        var shipments = await _shipmentRepository.Query()
            .Include(s => s.Order)
                .ThenInclude(o => o.Customer)
            .Where(s => !s.IsDeleted &&
                       (s.TrackingNumber.Contains(searchTerm) ||
                        s.Carrier.Contains(searchTerm) ||
                        s.Order.OrderNumber.Contains(searchTerm) ||
                        s.Order.Customer.NameSurname.Contains(searchTerm)))
            .OrderByDescending(s => s.CreatedAt)
            .ToListAsync();

        return shipments.Select(s => new ShipmentDto
        {
            Id = s.Id,
            OrderId = s.OrderId,
            TrackingNumber = s.TrackingNumber,
            Carrier = s.Carrier,
            Status = s.Status,
            ShippedAt = s.ShippedAt,
            DeliveredAt = s.DeliveredAt,
            Notes = s.Notes,
            CreatedAt = s.CreatedAt
        });
    }

    public async Task<ShipmentDto> CreateAsync(CreateShipmentDto dto)
    {
        // Verify order exists
        var order = await _orderRepository.GetByIdAsync(dto.OrderId);
        if (order == null || order.IsDeleted)
            throw new ArgumentException("Order not found");

        var shipment = new Shipment
        {
            Id = Guid.CreateVersion7(),
            OrderId = dto.OrderId,
            TrackingNumber = dto.TrackingNumber,
            Carrier = dto.Carrier,
            Status = dto.Status,
            ShippedAt = dto.ShippedAt,
            DeliveredAt = dto.DeliveredAt,
            Notes = dto.Notes,
            EmployeeId = _currentUserService.UserId, // Mevcut kullanıcıyı set et
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _shipmentRepository.AddAsync(shipment);
        await _shipmentRepository.SaveChangesAsync(); // SaveChanges çağrısını ekle

        return new ShipmentDto
        {
            Id = shipment.Id,
            OrderId = shipment.OrderId,
            TrackingNumber = shipment.TrackingNumber,
            Carrier = shipment.Carrier,
            Status = shipment.Status,
            ShippedAt = shipment.ShippedAt,
            DeliveredAt = shipment.DeliveredAt,
            Notes = shipment.Notes,
            CreatedAt = shipment.CreatedAt
        };
    }

    public async Task<ShipmentDto> UpdateAsync(Guid id, UpdateShipmentDto dto)
    {
        var shipment = await _shipmentRepository.GetByIdAsync(id);
        if (shipment == null || shipment.IsDeleted)
            throw new ArgumentException("Shipment not found");

        if (!string.IsNullOrEmpty(dto.TrackingNumber))
            shipment.TrackingNumber = dto.TrackingNumber;
        if (!string.IsNullOrEmpty(dto.Carrier))
            shipment.Carrier = dto.Carrier;
        if (dto.Status.HasValue)
            shipment.Status = dto.Status.Value;
        if (dto.ShippedAt.HasValue)
            shipment.ShippedAt = dto.ShippedAt.Value;
        if (dto.DeliveredAt.HasValue)
            shipment.DeliveredAt = dto.DeliveredAt.Value;
        if (dto.Notes != null)
            shipment.Notes = dto.Notes;

        shipment.UpdatedAt = DateTime.UtcNow;

        _shipmentRepository.Update(shipment);
        await _shipmentRepository.SaveChangesAsync();

        return new ShipmentDto
        {
            Id = shipment.Id,
            OrderId = shipment.OrderId,
            TrackingNumber = shipment.TrackingNumber,
            Carrier = shipment.Carrier,
            Status = shipment.Status,
            ShippedAt = shipment.ShippedAt,
            DeliveredAt = shipment.DeliveredAt,
            Notes = shipment.Notes,
            CreatedAt = shipment.CreatedAt
        };
    }

    public async Task<bool> DeleteAsync(Guid id)
    {
        var shipment = await _shipmentRepository.GetByIdAsync(id);
        if (shipment == null || shipment.IsDeleted)
            return false;

        shipment.IsDeleted = true;
        shipment.UpdatedAt = DateTime.UtcNow;

        _shipmentRepository.Update(shipment);
        await _shipmentRepository.SaveChangesAsync();
        return true;
    }

    public async Task<bool> UpdateStatusAsync(Guid id, ShipmentStatus status)
    {
        var shipment = await _shipmentRepository.GetByIdAsync(id);
        if (shipment == null || shipment.IsDeleted)
            return false;

        shipment.Status = status;
        if (status == ShipmentStatus.Delivered && !shipment.DeliveredAt.HasValue)
            shipment.DeliveredAt = DateTime.UtcNow;
        
        shipment.UpdatedAt = DateTime.UtcNow;

        _shipmentRepository.Update(shipment);
        await _shipmentRepository.SaveChangesAsync();
        return true;
    }

    public async Task<ShipmentDto?> GetByTrackingNumberAsync(string trackingNumber)
    {
        var shipment = await _shipmentRepository.Query()
            .Include(s => s.Order)
                .ThenInclude(o => o.Customer)
            .FirstOrDefaultAsync(s => s.TrackingNumber == trackingNumber && !s.IsDeleted);

        if (shipment == null) return null;

        return new ShipmentDto
        {
            Id = shipment.Id,
            OrderId = shipment.OrderId,
            TrackingNumber = shipment.TrackingNumber,
            Carrier = shipment.Carrier,
            Status = shipment.Status,
            ShippedAt = shipment.ShippedAt,
            DeliveredAt = shipment.DeliveredAt,
            Notes = shipment.Notes,
            CreatedAt = shipment.CreatedAt
        };
    }
}
