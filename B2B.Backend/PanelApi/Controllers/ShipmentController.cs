using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Enums;
using Core.Events;
using MassTransit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using PanelApi.Attributes;
using Shipping.Abstraction;

namespace PanelApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[EnableCors("AllowFrontend")]
[Authorize]
public class ShipmentController : ControllerBase
{
    private readonly IShipmentService _shipmentService;
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly IShippingServiceFactory _shippingServiceFactory;
    private readonly IShippingCarrierService _shippingCarrierService;

    public ShipmentController(
        IShipmentService shipmentService,
        IPublishEndpoint publishEndpoint,
        IShippingServiceFactory shippingServiceFactory,
        IShippingCarrierService shippingCarrierService)
    {
        _shipmentService = shipmentService;
        _publishEndpoint = publishEndpoint;
        _shippingServiceFactory = shippingServiceFactory;
        _shippingCarrierService = shippingCarrierService;
    }

    [HttpGet]
    [RequirePermission("shipping", "read")]
    public async Task<ActionResult<IEnumerable<ShipmentDto>>> GetAll()
    {
        try
        {
            var shipments = await _shipmentService.GetAllAsync();
            return Ok(shipments);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("{id}")]
    [RequirePermission("shipping", "read")]
    public async Task<ActionResult<ShipmentDto>> GetById(Guid id)
    {
        try
        {
            var shipment = await _shipmentService.GetByIdAsync(id);
            if (shipment == null)
                return NotFound(new { message = "Shipment not found" });

            return Ok(shipment);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("order/{orderId}")]
    [RequirePermission("shipping", "read")]
    public async Task<ActionResult<IEnumerable<ShipmentDto>>> GetByOrderId(Guid orderId)
    {
        try
        {
            var shipments = await _shipmentService.GetByOrderIdAsync(orderId);
            return Ok(shipments);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("status/{status}")]
    [RequirePermission("shipping", "read")]
    public async Task<ActionResult<IEnumerable<ShipmentDto>>> GetByStatus(ShipmentStatus status)
    {
        try
        {
            var shipments = await _shipmentService.GetByStatusAsync(status);
            return Ok(shipments);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("tracking/{trackingNumber}")]
    [RequirePermission("shipping", "read")]
    public async Task<ActionResult<ShipmentDto>> GetByTrackingNumber(string trackingNumber)
    {
        try
        {
            var shipment = await _shipmentService.GetByTrackingNumberAsync(trackingNumber);
            if (shipment == null)
                return NotFound(new { message = "Shipment not found" });

            return Ok(shipment);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("search")]
    [RequirePermission("shipping", "read")]
    public async Task<ActionResult<IEnumerable<ShipmentDto>>> Search([FromQuery] string searchTerm)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return BadRequest(new { message = "Search term is required" });

            var shipments = await _shipmentService.SearchAsync(searchTerm);
            return Ok(shipments);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost]
    [RequirePermission("shipping", "create")]
    public async Task<ActionResult<ShipmentDto>> Create([FromBody] CreateShipmentDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var shipment = await _shipmentService.CreateAsync(dto);
            return Ok(new { data = shipment, message = "Shipment created successfully" });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Kargo firması ile kargo oluştur (Event-driven)
    /// </summary>
    [HttpPost("create-with-carrier")]
    [RequirePermission("shipping", "create")]
    public async Task<ActionResult> CreateWithCarrier([FromBody] CreateShipmentWithCarrierDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            // Event'i fırlat
            await _publishEndpoint.Publish(new ShipmentCreateRequested
            {
                OrderId = dto.OrderId,
                CarrierId = dto.CarrierId,
                CargoKey = dto.CargoKey,
                InvoiceKey = dto.InvoiceKey,
                RecipientName = dto.RecipientName,
                RecipientPhone = dto.RecipientPhone,
                RecipientEmail = dto.RecipientEmail,
                DeliveryAddress = dto.DeliveryAddress,
                City = dto.City,
                District = dto.District,
                PostalCode = dto.PostalCode,
                Weight = dto.Weight,
                DeclaredValue = dto.DeclaredValue,
                SpecialInstructions = dto.SpecialInstructions,
                RequestedByUserId = dto.RequestedByUserId
            });

            return Ok(new { message = "Shipment creation request submitted successfully" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPut("{id}")]
    [RequirePermission("shipping", "update")]
    public async Task<ActionResult<ShipmentDto>> Update(Guid id, [FromBody] UpdateShipmentDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var shipment = await _shipmentService.UpdateAsync(id, dto);
            return Ok(new { data = shipment, message = "Shipment updated successfully" });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPut("{id}/status")]
    [RequirePermission("shipping", "update")]
    public async Task<ActionResult> UpdateStatus(Guid id, [FromBody] UpdateShipmentStatusDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var success = await _shipmentService.UpdateStatusAsync(id, dto.Status);
            if (!success)
                return NotFound(new { message = "Shipment not found" });

            return Ok(new { message = "Shipment status updated successfully" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("{id}/cancel")]
    [RequirePermission("shipping", "update")]
    public async Task<ActionResult> CancelShipment(Guid id)
    {
        try
        {
            var shipment = await _shipmentService.GetByIdAsync(id);
            if (shipment == null)
                return NotFound(new { message = "Shipment not found" });

            // Kargo firması servisini ShortCode ile al
            var shippingService = _shippingServiceFactory.GetService(shipment.CarrierShortCode);
            if (shippingService == null)
                return BadRequest(new { message = "Shipping service not found for this carrier" });

            // Kargo firmasından iptal et
            var cancelSuccess = await shippingService.CancelShipmentAsync(shipment.TrackingNumber);
            if (!cancelSuccess)
                return BadRequest(new { message = "Failed to cancel shipment with carrier" });

            // Veritabanında durumu güncelle
            var updateSuccess = await _shipmentService.UpdateStatusAsync(id, ShipmentStatus.Cancelled);
            if (!updateSuccess)
                return BadRequest(new { message = "Failed to update shipment status" });

            return Ok(new { message = "Shipment cancelled successfully" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpDelete("{id}")]
    [RequirePermission("shipping", "delete")]
    public async Task<ActionResult> Delete(Guid id)
    {
        try
        {
            var success = await _shipmentService.DeleteAsync(id);
            if (!success)
                return NotFound(new { message = "Shipment not found" });

            return Ok(new { message = "Shipment deleted successfully" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Kargo takip numarasını güncelle
    /// </summary>
    [HttpPut("{id}/tracking-number")]
    [RequirePermission("shipping", "update")]
    public async Task<ActionResult> UpdateTrackingNumber(Guid id, [FromBody] UpdateTrackingNumberDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var updateDto = new UpdateShipmentDto
            {
                TrackingNumber = dto.TrackingNumber
            };

            var shipment = await _shipmentService.UpdateAsync(id, updateDto);
            return Ok(new { data = shipment, message = "Tracking number updated successfully" });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Kargo firması ile kargo oluştur ve takip numarası al
    /// </summary>
    [HttpPost("create-shipment-with-carrier/{orderId}")]
    [RequirePermission("shipping", "create")]
    public async Task<ActionResult> CreateShipmentWithCarrier(Guid orderId, [FromBody] CreateShipmentWithCarrierRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            // Kargo firmasını ShortCode ile bul
            var carriers = await _shippingCarrierService.GetAllAsync();
            var carrier = carriers.FirstOrDefault(c => c.ShortCode == dto.CarrierShortCode);
            if (carrier == null)
                return BadRequest(new { message = $"Carrier not found: {dto.CarrierShortCode}" });

            // Kargo servisini al
            var shippingService = _shippingServiceFactory.GetService(dto.CarrierShortCode);
            if (shippingService == null)
                return BadRequest(new { message = $"Shipping service not found for carrier: {dto.CarrierShortCode}" });

            // Kargo oluşturma isteği hazırla
            var shipmentRequest = new ShipmentRequest
            {
                OrderId = orderId,
                RecipientName = dto.RecipientName,
                RecipientPhone = dto.RecipientPhone,
                RecipientEmail = dto.RecipientEmail,
                Address = dto.Address,
                City = dto.City,
                District = dto.District,
                PostalCode = dto.PostalCode,
                Weight = dto.Weight ?? 1.0m,
                DeclaredValue = dto.DeclaredValue,
                SpecialInstructions = dto.SpecialInstructions
            };

            // Kargo oluştur
            var trackingNumber = await shippingService.CreateShipmentAsync(shipmentRequest);

            // Shipment kaydını oluştur
            var createShipmentDto = new CreateShipmentDto
            {
                OrderId = orderId,
                TrackingNumber = trackingNumber,
                CarrierId = carrier.Id,
                Status = ShipmentStatus.Processing,
                ShippedAt = DateTime.UtcNow,
                Notes = "Kargo firması ile otomatik oluşturuldu"
            };

            var shipment = await _shipmentService.CreateAsync(createShipmentDto);

            return Ok(new {
                data = shipment,
                trackingNumber = trackingNumber,
                message = "Shipment created successfully with carrier"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }
}

public class UpdateShipmentStatusDto
{
    public ShipmentStatus Status { get; set; }
}

public class UpdateTrackingNumberDto
{
    public string TrackingNumber { get; set; } = null!;
}

public class CreateShipmentWithCarrierRequestDto
{
    public string CarrierShortCode { get; set; } = null!;
    public string RecipientName { get; set; } = null!;
    public string RecipientPhone { get; set; } = null!;
    public string? RecipientEmail { get; set; }
    public string Address { get; set; } = null!;
    public string City { get; set; } = null!;
    public string District { get; set; } = null!;
    public string? PostalCode { get; set; }
    public decimal? Weight { get; set; }
    public decimal? DeclaredValue { get; set; }
    public string? SpecialInstructions { get; set; }
}
